using ChatApp.Backend.Models;

namespace ChatApp.Backend.Hub;
using Microsoft.AspNetCore.SignalR;
public class ChatHub(ILogger<ChatHub> logger) : Hub

{
    
    private readonly ILogger<ChatHub> _logger;

    public ChatHub(ILogger<ChatHub> logger)
    {
        _logger = logger;
    }
    public async Task JoinChat(UserConnection userConnection)
    {
        logger.LogInformation($"🔄 User {userConnection.UserName} joining general chat. ConnectionId: {Context.ConnectionId}");
        await Clients.All.SendAsync("ReceiveChat", "admin" ,$"{userConnection.UserName} has joined the chat");
        _logger.LogInformation($"✅ User {userConnection.UserName} successfully joined general chat");
    }
    

    public async Task JoinSpecificChatRoom(UserConnection userConnection)
    {
        _logger.LogInformation($"🔄 User {userConnection.UserName} joining room '{userConnection.ChatRoom}'. ConnectionId: {Context.ConnectionId}");
        
        await Groups.AddToGroupAsync(Context.ConnectionId, userConnection.ChatRoom);
        await Clients.Group(userConnection.ChatRoom).SendAsync("ReceiveChat", "admin", $"{userConnection.UserName} has joined {userConnection.ChatRoom}");
        
        _logger.LogInformation($"✅ User {userConnection.UserName} successfully joined room '{userConnection.ChatRoom}'");
    }

    public override async Task OnConnectedAsync()
    {
        _logger.LogInformation($"🔗 New connection established. ConnectionId: {Context.ConnectionId}");
        await base.OnConnectedAsync();
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        _logger.LogInformation($"🔌 Connection disconnected. ConnectionId: {Context.ConnectionId}");
        await base.OnDisconnectedAsync(exception);
    }

}